import type { Header } from '@tanstack/react-table'

export interface ColumnResizerProps<TData> {
  header: Header<TData, unknown>
}

export const ColumnResizer = <TData,>({
  header,
}: {
  header: Header<TData, unknown>
}) => {
  if (header.column.getCanResize() === false) return <></>

  return (
    <div
      onMouseDown={header.getResizeHandler()}
      onTouchStart={header.getResizeHandler()}
      className={
        'absolute top-0 right-0 cursor-col-resize w-1 h-full bg-border hover:bg-primary hover:w-2 transition-all duration-150'
      }
      style={{
        userSelect: 'none',
        touchAction: 'none',
      }}
    />
  )
}
