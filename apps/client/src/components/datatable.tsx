'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useWhatChanged } from '@simbathesailor/use-what-changed'
import {
  type ColumnDef,
  type ColumnSizingState,
  type Row,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  type KeyboardEvent,
  type RefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useRefify } from '~/lib/hooks/use-refify'
import { cnByObject } from '~/lib/util/cn-by-object'
import { ColumnResizer } from './column-resizer'

// Define the ref interface for DataTable
export interface DataTableRef {
  focus: () => void
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onRowClick?: (row: TData) => void
  onRowDoubleClick?: (row: TData) => void
  onRowKeyDown?: (e: KeyboardEvent<HTMLTableRowElement>, row: TData) => void
  getRowId?: (row: TData) => string
  onFocusedRowIdChanged?: (rowId: string | undefined) => void
  selectedRowId?: string
  highlightedRowId?: string
  stickyHeaders?: boolean
  clickDelay?: number // Delay in ms to distinguish between single and double clicks
  ref?: RefObject<DataTableRef | null>
  keepFocused?: boolean
  debug?: boolean
}

export function DataTable<TData, TValue>({
  columns,
  data,
  onRowClick,
  onRowDoubleClick,
  onRowKeyDown,
  onFocusedRowIdChanged,
  getRowId,
  selectedRowId,
  highlightedRowId,
  clickDelay = 250, // Default delay of 250ms
  stickyHeaders = false,
  keepFocused = false,
  debug = false,
  ref,
}: DataTableProps<TData, TValue>) {
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1)
  const tableRef = useRef<HTMLTableElement>(null)
  const rowRefs = useRef<(HTMLTableRowElement | null)[]>([])
  const clickTimerRef = useRef<number | null>(null)
  const clickedRowRef = useRef<Row<TData> | null>(null)
  const [colSizing, setColSizing] = useState<ColumnSizingState>({})

  // Initialize the table
  const table = useReactTable({
    data,
    columns,
    enableColumnResizing: true,
    columnResizeMode: 'onChange',
    onColumnSizingChange: setColSizing,
    state: {
      columnSizing: colSizing,
    },
    getCoreRowModel: getCoreRowModel(),
    getRowId: getRowId ? (row) => getRowId(row as TData) : undefined,
    enableMultiRowSelection: false,
  })

  const selectedRowIdRef = useRefify(selectedRowId)
  const tableApiRef = useRefify(table)

  // Create a focus function that can be called via the ref
  const focus = useCallback(() => {
    // Always focus the table element first to ensure it receives keyboard events
    tableRef.current?.focus()

    // Then, if there are rows, focus the appropriate row
    if (data.length > 0) {
      const selRowId = selectedRowIdRef.current
      if (selRowId) {
        const rowModel = tableApiRef.current.getRowModel()
        const index = rowModel.rows.findIndex((row) => row.id === selRowId)

        if (index !== -1) {
          setFocusedRowIndex(index)
          rowRefs.current[index]?.focus()
          return
        }
      }

      // Focus the first row if no row is selected
      setFocusedRowIndex(0)
      rowRefs.current[0]?.focus()
    }
    // If no rows, we've already focused the table element
  }, [data.length, selectedRowIdRef, tableApiRef])

  useWhatChanged(
    [focusedRowIndex, selectedRowId],
    'focusedRowIndex, selectedRowId',
    'datatable'
  )

  // Expose the focus method via ref (React 19 style)
  if (ref) {
    ref.current = { focus }
  }

  // Initialize row refs array
  useEffect(() => {
    rowRefs.current = rowRefs.current.slice(0, data.length)
  }, [data])

  const onFocusedRowIdChangedRef = useRefify(onFocusedRowIdChanged)
  // Handle keyboard navigation
  const handleTableKeyDown = useCallback<
    (e: KeyboardEvent<HTMLTableElement>) => void
  >(
    (e) => {
      // Allow keyboard events even when there are no rows
      // This ensures we can still handle navigation keys like left/right arrows
      if (!data.length) {
        // For empty tables, we still want to handle keyboard events
        // but we'll only pass them to the parent component
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          // Let parent component handle left/right navigation
          return
        }

        // For other keys, prevent default behavior
        e.preventDefault()
        return
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          console.log('arrow down received')
          setFocusedRowIndex((prev) => {
            const nextIndex = prev < data.length - 1 ? prev + 1 : 0
            const nextElementToFocus = rowRefs.current[nextIndex]
            console.log(
              'trying to focus next element',
              { prev, nextIndex },
              nextElementToFocus
            )
            nextElementToFocus?.focus()
            onFocusedRowIdChangedRef.current?.(
              nextElementToFocus?.id ?? undefined
            )
            return nextIndex
          })
          break
        case 'ArrowUp':
          e.preventDefault()
          setFocusedRowIndex((prev) => {
            const nextIndex = prev > 0 ? prev - 1 : data.length - 1
            rowRefs.current[nextIndex]?.focus()
            onFocusedRowIdChangedRef.current?.(rowRefs.current[nextIndex]?.id)
            return nextIndex
          })
          break
        case 'Home':
          e.preventDefault()
          setFocusedRowIndex(0)
          rowRefs.current[0]?.focus()
          onFocusedRowIdChangedRef.current?.(rowRefs.current[0]?.id)
          break
        case 'End':
          e.preventDefault()
          setFocusedRowIndex(data.length - 1)
          rowRefs.current[data.length - 1]?.focus()
          onFocusedRowIdChangedRef.current?.(
            rowRefs.current[data.length - 1]?.id
          )
          break
        case 'PageDown':
          e.preventDefault()
          setFocusedRowIndex((prev) => {
            // Jump 5 rows down or to the end
            const nextIndex = Math.min(prev + 5, data.length - 1)
            rowRefs.current[nextIndex]?.focus()
            onFocusedRowIdChangedRef.current?.(rowRefs.current[nextIndex]?.id)
            return nextIndex
          })
          break
        case 'PageUp':
          e.preventDefault()
          setFocusedRowIndex((prev) => {
            // Jump 5 rows up or to the beginning
            const nextIndex = Math.max(prev - 5, 0)
            rowRefs.current[nextIndex]?.focus()
            onFocusedRowIdChangedRef.current?.(rowRefs.current[nextIndex]?.id)
            return nextIndex
          })
          break
      }
    },
    [data.length, onFocusedRowIdChangedRef]
  )

  // Handle row keyboard events
  const handleRowKeyDown = (
    e: KeyboardEvent<HTMLTableRowElement>,
    row: Row<TData>
  ) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      // Enter key triggers the double-click action (open editor)
      onRowDoubleClick?.(row.original)
    } else if (e.key === ' ') {
      e.preventDefault()
      // Space key triggers the single-click action (select)
      onRowClick?.(row.original)
    }

    // Pass the event to the parent component if needed
    onRowKeyDown?.(e, row.original)
  }

  // Handle row click with single/double click detection
  const handleRowClick = (row: Row<TData>) => {
    // If this is a double click (same row clicked twice within the delay)
    if (clickedRowRef.current === row && clickTimerRef.current !== null) {
      // Clear the timer to prevent the single click handler from firing
      window.clearTimeout(clickTimerRef.current)
      clickTimerRef.current = null
      clickedRowRef.current = null

      // Handle double click
      onRowDoubleClick?.(row.original)
    } else {
      // Store the clicked row for potential double click detection
      clickedRowRef.current = row

      // Clear any existing timer
      if (clickTimerRef.current !== null) {
        window.clearTimeout(clickTimerRef.current)
      }

      // Set a timer for single click handling
      clickTimerRef.current = window.setTimeout(() => {
        // Handle single click after the delay
        onRowClick?.(row.original)
        clickTimerRef.current = null
        clickedRowRef.current = null
      }, clickDelay)
    }
  }

  const dataLength = data.length
  // Set focus to the selected row when it changes
  useEffect(() => {
    if (dataLength) {
      if (selectedRowId) {
        const index = table
          .getRowModel()
          .rows.findIndex((row) => row.id === selectedRowId)

        console.log('focused row changed', index, selectedRowId)
        if (index !== -1) {
          setFocusedRowIndex(index)
          // Don't auto-focus on initial render to avoid unexpected focus changes
          if (focusedRowIndex !== -1) {
            rowRefs.current[index]?.focus()
          }
        }
      } else {
        setFocusedRowIndex(-1)
      }
    } else {
      setFocusedRowIndex(-1)
    }
  }, [selectedRowId, dataLength, table, focusedRowIndex])

  useFocusDebugging(tableRef, keepFocused)

  if (debug) {
    console.log('render datatable', focusedRowIndex, selectedRowId)
  }
  return (
    <div className="rounded-md border">
      {onRowDoubleClick && (
        <div className="text-xs text-muted-foreground p-2 border-b">
          <span>Tip: Single click to select, double click to edit</span>
        </div>
      )}
      <Table
        ref={tableRef}
        onKeyDown={handleTableKeyDown}
        aria-label="Data table"
        tabIndex={0}
      >
        <TableHeader
          className={cnByObject({
            'sticky top-0 z-10 bg-accent': stickyHeaders,
          })}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead
                    className="relative"
                    key={header.id}
                    style={{ width: `${header.getSize()}px` }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    <ColumnResizer header={header} />
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row, index) => {
              // Set up ref callback properly
              const setRowRef = (el: HTMLTableRowElement | null) => {
                rowRefs.current[index] = el
              }

              return (
                <TableRow
                  key={row.id}
                  ref={setRowRef}
                  data-state={
                    (row.getIsSelected() || row.id === selectedRowId) &&
                    'selected'
                  }
                  tabIndex={0}
                  onClick={() => handleRowClick(row)}
                  onDoubleClick={() => onRowDoubleClick?.(row.original)}
                  onKeyDown={(e) => handleRowKeyDown(e, row)}
                  aria-selected={row.id === selectedRowId}
                  aria-rowindex={index + 1}
                  className={cnByObject({
                    'cursor-pointer transition-colors duration-100': true,
                    'outline-2 outline-primary ring-2 ring-primary/30':
                      focusedRowIndex === index,
                    'bg-chart-2': highlightedRowId === row.id,
                    'hover:bg-muted/50': !!onRowDoubleClick,
                  })}
                  title={onRowDoubleClick ? 'Double-click to edit' : ''}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              )
            })
          ) : (
            <TableRow
              tabIndex={0}
              onKeyDown={(e) => {
                // Handle keyboard events for the empty state row
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                  // Let the event bubble up to the table
                  return
                }
                e.preventDefault()
              }}
              className="focus:outline-none focus:ring-2 focus:ring-primary/30"
            >
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
        <TableFooter>
          {table.getFooterGroups().map((footerGroup) => (
            <TableRow key={footerGroup.id}>
              {footerGroup.headers.map((header) => (
                <TableCell key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.footer,
                        header.getContext()
                      )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableFooter>
      </Table>
    </div>
  )
}

function useFocusDebugging(
  tableRef: RefObject<HTMLTableElement | null>,
  keepFocused: boolean
) {
  useEffect(() => {
    const handleFocus = () => console.log('Component gained focus')
    const handleBlur = (e: FocusEvent) => {
      // console.log('Component lost focus', document.activeElement, {
      //   relTarget: e.relatedTarget,
      // })

      // Only attempt to recapture focus if keepFocused is true
      if (keepFocused) {
        const relatedTarget = e.relatedTarget as HTMLElement | null

        // Don't recapture focus if:
        // 1. Focus is moving to a button, input, or other interactive element
        // 2. Focus is moving to null (likely leaving the window)
        // 3. Focus is moving to an element with role="button" or other interactive roles
        if (relatedTarget) {
          const tagNameOfActive = document.activeElement?.tagName.toLowerCase()

          if (tagNameOfActive !== 'body') {
            // Allow focus to move to interactive elements
            return
          }
        }

        // Check if the focus moved within the table component
        const wasChild = tableRef.current?.contains(e.relatedTarget as Node)

        // Only recapture focus if it was moving between elements within the table
        if (wasChild) {
          console.log('Focus moved within table, recapturing')
          // Use a shorter timeout to reduce the perception of lag
          setTimeout(() => {
            tableRef.current?.focus()
          }, 50)
        }
      }
    }

    const element = tableRef.current

    if (element) {
      element.addEventListener('focus', handleFocus, true)
      element.addEventListener('blur', handleBlur, true)
    }

    return () => {
      if (element) {
        element.removeEventListener('focus', handleFocus, true)
        element.removeEventListener('blur', handleBlur, true)
      }
    }
  }, [tableRef, keepFocused])
}
